import utils
from Classifier import Classifier
from OCR import ocr

file_path = 'data/Faktura.pdf'

print("🔍 OCR Document Processing with Classification Results")
print("=" * 55)

# Step 1: OCR Processing
print("1. Performing OCR...")
df = ocr.do(file_path)
print(f"   ✓ Found {len(df)} raw text elements")

# Step 2: Text Processing
print("2. Processing text data...")
df = utils.merge_texts(df)
print(f"   ✓ After merging: {len(df)} elements")

# Step 3: Value Classification
print("3. Performing value classification...")
utils.classify_batch_values(df)
print("   ✓ Value classification completed")

# Step 4: Text Cleaning
print("4. Cleaning texts...")
df = utils.clean_texts(df)
print("   ✓ Text cleaning completed")

# Step 5: Key Classification
print("5. Performing key classification...")

# Inicializace klasifikátoru
classifier = Classifier(model_name='paraphrase-multilingual-mpnet-base-v2')

# Načtení modelu
print("   Loading trained model...")
model_loaded = classifier.load_model('model_paraphrase-multilingual-mpnet-base-v2')

# Načtení mapování kategorií z Excel souboru (MUSÍ být po načtení modelu!)
print("   Loading category mapping from Excel...")
mapping = utils.load_key_class_mapping_from_xlsx()
if mapping:
    print(f"   ✓ Loaded {len(mapping)} categories from Excel")
    classifier.set_category_id_mapping(mapping)
else:
    print("   ⚠️  Failed to load category mapping from Excel")

if not model_loaded:
    print("   ⚠️  Failed to load model, key classification will be skipped")
    # Přidáme prázdný key_class sloupec
    df['key_class'] = 0
    df['similarity'] = 0.0
else:
    print("   ✓ Model loaded successfully")

    # Klasifikace textů s číselnými identifikátory
    print("   Classifying texts...")
    df = classifier.batch_classify(
        df=df,
        text_column='text',
        class_column='key_class',
        similarity_column='similarity',
        threshold=0.85,
        use_numeric_ids=True  # Používáme číselné identifikátory místo názvů kategorií
    )

    # Statistiky key_class
    key_classified = len(df[df['key_class'] > 0])
    #df = utils.postprocess(df)
    print(f"   ✓ Key classification completed: {key_classified}/{len(df)} elements classified")

# Step 6: Display Classification Results
print("\n6. Classification Results:")
print("=" * 55)

# Zobrazíme celkové statistiky
total_elements = len(df)
value_classified = len(df[df['value_class'] > 0])
key_classified = len(df[df['key_class'] > 0])

print(f"📊 Total elements: {total_elements}")
print(f"📊 Value classified elements: {value_classified}")
print(f"📊 Key classified elements: {key_classified}")

# Zobrazíme rozložení podle value_class
if value_classified > 0:
    print(f"\n📋 Value class breakdown:")
    value_counts = df[df['value_class'] > 0]['value_class'].value_counts().sort_index()
    for class_id, count in value_counts.items():
        class_text = utils.get_value_class_name(class_id)
        print(f"   {class_id} ({class_text}): {count} elements")

# Zobrazíme rozložení podle key_class
if key_classified > 0:
    print(f"\n🔑 Key class breakdown:")
    key_counts = df[df['key_class'] > 0]['key_class'].value_counts().sort_index()
    for class_id, count in key_counts.items():
        class_text = utils.get_key_class_name(class_id)
        print(f"   {class_id} ({class_text}): {count} elements")

# Zobrazíme detailní výsledky pro key klasifikované prvky
print(f"\n🔍 Detailed Key Classification Results:")
print("-" * 80)

key_classified_df = df[df['key_class'] > 0].copy()
if not key_classified_df.empty:
    # Seřadíme podle key_class a similarity (sestupně)
    key_classified_df = key_classified_df.sort_values(['key_class', 'similarity'], ascending=[True, False])
    
    for idx, row in key_classified_df.iterrows():
        text = row['text']
        key_class = row['key_class']
        similarity = row['similarity']
        value_class = row['value_class']
        
        # Získáme názvy kategorií
        key_class_name = utils.get_key_class_name(key_class)
        value_class_name = utils.get_value_class_name(value_class)
        
        # Zaokrouhlíme podobnost na 2 desetinná místa
        similarity_rounded = round(similarity, 4)
        
        print(f"Text: '{text}' ({key_class_name}) {similarity_rounded}")
        # print(f"   Key Class: {key_class} ({key_class_name})")
        # print(f"   Value Class: {value_class} ({value_class_name})")
        # print(f"   Similarity: {similarity_rounded}")
        # print()
else:
    print("   No key classified elements found.")
exit()
# Zobrazíme také prvky s vysokou value_class ale bez key_class
print(f"\n📝 Value-only Classification Results (no key classification):")
print("-" * 80)

value_only_df = df[(df['value_class'] > 0) & (df['key_class'] == 0)].copy()
if not value_only_df.empty:
    # Seřadíme podle value_class
    value_only_df = value_only_df.sort_values('value_class')
    
    for idx, row in value_only_df.iterrows():
        text = row['text']
        value_class = row['value_class']
        
        # Získáme název kategorie
        value_class_name = utils.get_value_class_name(value_class)
        
        print(f"Text: '{text}'")
        print(f"   Value Class: {value_class} ({value_class_name})")
        print()
else:
    print("   No value-only classified elements found.")

print("✅ Classification analysis completed!")
