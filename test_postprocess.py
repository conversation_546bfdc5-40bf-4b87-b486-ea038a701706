#!/usr/bin/env python3
"""
Test script pro metodu postprocess v utils.py
"""

import pandas as pd
import utils

def create_test_data():
    """Vytvoří testovací data pro postprocess metodu."""
    
    # Testovací data s r<PERSON><PERSON><PERSON><PERSON><PERSON> tř<PERSON>dami a similaritami
    test_data = [
        # IČO (18) - mělo by z<PERSON><PERSON><PERSON> max 2 s nej<PERSON><PERSON><PERSON><PERSON><PERSON> similaritami
        {'text': 'IČO: 12345678', 'key_class': 18, 'similarity': 0.95, 'left': 100, 'top': 100, 'width': 80, 'height': 20},
        {'text': 'IČO: 87654321', 'key_class': 18, 'similarity': 0.92, 'left': 100, 'top': 120, 'width': 80, 'height': 20},
        {'text': 'IČO: 11111111', 'key_class': 18, 'similarity': 0.88, 'left': 100, 'top': 140, 'width': 80, 'height': 20},
        {'text': 'IČO: 22222222', 'key_class': 18, 'similarity': 0.85, 'left': 100, 'top': 160, 'width': 80, 'height': 20},
        
        # DIČ (20) - mělo by <PERSON><PERSON><PERSON><PERSON> max 2 s nejvy<PERSON><PERSON>ími similaritami  
        {'text': 'DI<PERSON>: CZ12345678', 'key_class': 20, 'similarity': 0.93, 'left': 200, 'top': 100, 'width': 90, 'height': 20},
        {'text': 'DI<PERSON>: CZ87654321', 'key_class': 20, 'similarity': 0.90, 'left': 200, 'top': 120, 'width': 90, 'height': 20},
        {'text': 'DIČ: CZ11111111', 'key_class': 20, 'similarity': 0.87, 'left': 200, 'top': 140, 'width': 90, 'height': 20},
        
        # Číslo objednávky (8) - mělo by zůstat max 2 s nejvyššími similaritami
        {'text': 'Objednávka: ORD001', 'key_class': 8, 'similarity': 0.91, 'left': 300, 'top': 100, 'width': 100, 'height': 20},
        {'text': 'Objednávka: ORD002', 'key_class': 8, 'similarity': 0.89, 'left': 300, 'top': 120, 'width': 100, 'height': 20},
        {'text': 'Objednávka: ORD003', 'key_class': 8, 'similarity': 0.86, 'left': 300, 'top': 140, 'width': 100, 'height': 20},
        
        # Číslo faktury (5) - měl by zůstat max 1 s nejvyšší similaritou
        {'text': 'Faktura: F001', 'key_class': 5, 'similarity': 0.94, 'left': 400, 'top': 100, 'width': 80, 'height': 20},
        {'text': 'Faktura: F002', 'key_class': 5, 'similarity': 0.91, 'left': 400, 'top': 120, 'width': 80, 'height': 20},
        {'text': 'Faktura: F003', 'key_class': 5, 'similarity': 0.88, 'left': 400, 'top': 140, 'width': 80, 'height': 20},
        
        # Datum vystavení (12) - měl by zůstat max 1 s nejvyšší similaritou
        {'text': '15.12.2024', 'key_class': 12, 'similarity': 0.96, 'left': 500, 'top': 100, 'width': 70, 'height': 20},
        {'text': '16.12.2024', 'key_class': 12, 'similarity': 0.93, 'left': 500, 'top': 120, 'width': 70, 'height': 20},
        
        # Neklasifikované řádky (key_class = 0) - měly by zůstat všechny
        {'text': 'Nějaký text', 'key_class': 0, 'similarity': 0.0, 'left': 600, 'top': 100, 'width': 60, 'height': 20},
        {'text': 'Další text', 'key_class': 0, 'similarity': 0.0, 'left': 600, 'top': 120, 'width': 60, 'height': 20},
    ]
    
    return pd.DataFrame(test_data)

def test_postprocess():
    """Testuje postprocess metodu."""
    
    print("=== Test postprocess metody ===")
    print()
    
    # Vytvoříme testovací data
    df = create_test_data()
    
    print("Původní data:")
    print(f"Celkem řádků: {len(df)}")
    print(f"Klasifikovaných řádků (key_class > 0): {len(df[df['key_class'] > 0])}")
    print()
    
    # Zobrazíme rozložení podle tříd
    print("Rozložení podle key_class (před postprocessingem):")
    class_counts = df[df['key_class'] > 0]['key_class'].value_counts().sort_index()
    for class_id, count in class_counts.items():
        class_name = utils.get_key_class_name(class_id)
        print(f"  {class_id} ({class_name}): {count} výskytů")
    print()
    
    # Spustíme postprocessing
    print("Spouštím postprocessing...")
    print("-" * 50)
    filtered_df = utils.postprocess(df)
    print("-" * 50)
    print()
    
    # Zobrazíme výsledky
    print("Výsledky po postprocessingu:")
    print(f"Celkem řádků: {len(filtered_df)}")
    print(f"Klasifikovaných řádků (key_class > 0): {len(filtered_df[filtered_df['key_class'] > 0])}")
    print()
    
    # Zobrazíme rozložení podle tříd po filtrování
    print("Rozložení podle key_class (po postprocessingu):")
    filtered_class_counts = filtered_df[filtered_df['key_class'] > 0]['key_class'].value_counts().sort_index()
    for class_id, count in filtered_class_counts.items():
        class_name = utils.get_key_class_name(class_id)
        print(f"  {class_id} ({class_name}): {count} výskytů")
    print()
    
    # Detailní zobrazení ponechaných řádků
    print("Detailní výsledky (pouze klasifikované řádky):")
    classified_results = filtered_df[filtered_df['key_class'] > 0].sort_values(['key_class', 'similarity'], ascending=[True, False])
    
    for idx, row in classified_results.iterrows():
        class_name = utils.get_key_class_name(row['key_class'])
        similarity_rounded = round(row['similarity'], 3)
        print(f"  {row['key_class']} ({class_name}): '{row['text']}' (similarity: {similarity_rounded})")
    
    print()
    print("=== Test dokončen ===")

if __name__ == "__main__":
    test_postprocess()
